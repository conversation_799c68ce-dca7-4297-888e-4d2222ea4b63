"use client"
import React, { useEffect, useState } from 'react'
import { useParams } from 'next/navigation'

const API_BASE = process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:8000'

type Issue = {
  id: number
  issue_type?: string
  severity?: string
  description?: string
}

type KPI = {
  id: number
  kpi_name: string
  value?: number
  trend?: string
}

type Analysis = {
  id: number
  total_records?: number
  quality_score?: number
  issues: Issue[]
  kpis: KPI[]
}

export default function UploadDetail() {
  const params = useParams<{ id: string }>()
  const id = params?.id
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [analysis, setAnalysis] = useState<Analysis | null>(null)

  useEffect(() => {
    if (!id) return
    const fetchAnalysis = async () => {
      setLoading(true)
      try {
        const res = await fetch(`${API_BASE}/uploads/${id}/analysis`)
        if (!res.ok) throw new Error('Not ready yet or not found')
        const data = await res.json()
        setAnalysis(data)
      } catch (e: any) {
        setError(e.message)
      } finally {
        setLoading(false)
      }
    }
    fetchAnalysis()
  }, [id])

  return (
    <main className="space-y-6">
      <div className="evos-card">
        <h2 className="text-lg font-medium">Analysis for Upload {id}</h2>
        {loading && <div className="mt-4">Loading…</div>}
        {error && <div className="mt-4 text-error">{error}</div>}
        {analysis && (
          <div className="mt-4 space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="evos-card">
                <div className="text-sm text-slate">Total records</div>
                <div className="text-xl font-semibold">{analysis.total_records ?? '-'}</div>
              </div>
              <div className="evos-card">
                <div className="text-sm text-slate">Quality score</div>
                <div className="text-xl font-semibold">{analysis.quality_score?.toFixed(1) ?? '-'}</div>
              </div>
            </div>
            <div>
              <h3 className="text-md font-medium mb-2">Issues</h3>
              <ul className="space-y-2">
                {analysis.issues.map((i) => (
                  <li key={i.id} className="border border-gray-200 rounded-lg px-3 py-2">
                    <span className="font-medium">{i.issue_type}</span>
                    <span className="ml-2 text-slate">[{i.severity}]</span>
                    <div className="text-sm">{i.description}</div>
                  </li>
                ))}
                {analysis.issues.length === 0 && <div className="text-slate">No issues detected</div>}
              </ul>
            </div>
            <div>
              <h3 className="text-md font-medium mb-2">KPIs</h3>
              <ul className="space-y-2">
                {analysis.kpis.map((k) => (
                  <li key={k.id} className="border border-gray-200 rounded-lg px-3 py-2">
                    <span className="font-medium">{k.kpi_name}</span>
                    <span className="ml-2 text-slate">{k.value ?? '-'}</span>
                    {k.trend && <span className="ml-2 text-slate">({k.trend})</span>}
                  </li>
                ))}
                {analysis.kpis.length === 0 && <div className="text-slate">No KPIs computed</div>}
              </ul>
            </div>
          </div>
        )}
      </div>
    </main>
  )
}

