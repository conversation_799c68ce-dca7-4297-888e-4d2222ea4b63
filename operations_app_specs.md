# Operations Data Analysis Application
## Technical Specifications & Development Guide

### 🎯 Project Overview
A modern web application that transforms PowerBI exports into actionable insights for operations teams, combining automated data quality analysis with AI-powered recommendations.

---

## 🛠️ Recommended Technology Stack

### Backend Framework
**Primary Choice: Python/FastAPI**
```python
# Core Dependencies
fastapi==0.104.1
uvicorn[standard]==0.24.0
sqlalchemy==2.0.23
alembic==1.12.1
pandas==2.1.3
numpy==1.25.2
scikit-learn==1.3.2
redis==5.0.1
celery==5.3.4
python-multipart==0.0.6
```

**Database Stack:**
- **PostgreSQL 15+** - Primary database
- **TimescaleDB** - Time-series optimization
- **Redis 7+** - Caching & session management

**Alternative: Node.js/TypeScript**
```json
{
  "dependencies": {
    "express": "^4.18.2",
    "prisma": "^5.6.0",
    "@types/node": "^20.8.10",
    "typescript": "^5.2.2"
  }
}
```

### Frontend Framework
**React.js + TypeScript + Next.js 14**
```json
{
  "dependencies": {
    "next": "14.0.3",
    "react": "^18.2.0",
    "typescript": "^5.2.2",
    "tailwindcss": "^3.3.5",
    "recharts": "^2.8.0",
    "@tanstack/react-query": "^5.8.4",
    "zustand": "^4.4.6",
    "react-hook-form": "^7.47.0",
    "react-dropzone": "^14.2.3",
    "framer-motion": "^10.16.5"
  }
}
```

### Data Processing Libraries
```javascript
// Frontend Data Handling
import Papa from 'papaparse';        // CSV parsing
import * as XLSX from 'xlsx';        // Excel processing
import { format } from 'date-fns';   // Date manipulation
import _ from 'lodash';               // Utilities
```

### LLM Integration
- **Anthropic Claude API** - Primary choice for analysis
- **OpenAI GPT-4** - Alternative option
- **LangChain** - LLM orchestration
- **Pinecone/Weaviate** - Vector storage for patterns

---

## 🏗️ Application Architecture

### System Components Overview
```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   Frontend      │    │    Backend       │    │   AI Service   │
│   React/Next.js │◄──►│   FastAPI        │◄──►│   Claude API    │
│                 │    │   PostgreSQL     │    │   LangChain     │
└─────────────────┘    └──────────────────┘    └─────────────────┘
         │                       │                       │
         │                       │                       │
         ▼                       ▼                       ▼
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   File Storage  │    │   Cache Layer    │    │   Analytics     │
│   AWS S3/Local  │    │   Redis          │    │   Engine        │
└─────────────────┘    └──────────────────┘    └─────────────────┘
```

### Data Flow Pipeline
```
Excel Upload → Parse & Validate → Clean Data → Store → Analyze → Generate Insights → Dashboard Update
```

---

## 📊 Database Schema Design

### Core Tables
```sql
-- File Management
CREATE TABLE uploads (
    id SERIAL PRIMARY KEY,
    filename VARCHAR(255) NOT NULL,
    upload_date TIMESTAMP DEFAULT NOW(),
    file_hash VARCHAR(64) UNIQUE,
    status VARCHAR(50) DEFAULT 'processing',
    file_size_bytes BIGINT,
    row_count INTEGER,
    created_by INTEGER REFERENCES users(id)
);

-- Raw Data Storage
CREATE TABLE raw_order_data (
    id SERIAL PRIMARY KEY,
    upload_id INTEGER REFERENCES uploads(id),
    row_number INTEGER,
    terminal VARCHAR(100),
    order_id VARCHAR(100),
    date TIMESTAMP,
    customer_name VARCHAR(200),
    modality VARCHAR(50),
    vehicle_name VARCHAR(100),
    parcel_count INTEGER,
    product_group VARCHAR(100),
    product_name VARCHAR(200),
    direction VARCHAR(20),
    process_id VARCHAR(100),
    process_name VARCHAR(200),
    location VARCHAR(100),
    tank_number VARCHAR(50),
    country VARCHAR(100),
    weight_tons DECIMAL(10,3),
    transfer_rate_th DECIMAL(10,3),
    terminal_time_min INTEGER,
    prepump_time_min INTEGER,
    pump_time_min INTEGER,
    postpump_time_min INTEGER,
    arrival_time TIMESTAMP,
    accepted_nor_time TIMESTAMP,
    pumping_start_time TIMESTAMP,
    pumping_end_time TIMESTAMP,
    tendered_nor_time TIMESTAMP,
    departure_time TIMESTAMP
);

-- Analysis Results
CREATE TABLE analyses (
    id SERIAL PRIMARY KEY,
    upload_id INTEGER REFERENCES uploads(id),
    analysis_date TIMESTAMP DEFAULT NOW(),
    total_records INTEGER,
    quality_score DECIMAL(5,2),
    performance_score DECIMAL(5,2),
    error_count INTEGER,
    warning_count INTEGER,
    analysis_summary TEXT,
    created_by INTEGER REFERENCES users(id)
);

-- Data Quality Issues
CREATE TABLE data_quality_issues (
    id SERIAL PRIMARY KEY,
    analysis_id INTEGER REFERENCES analyses(id),
    order_id VARCHAR(100),
    issue_type VARCHAR(100),
    severity VARCHAR(20), -- 'critical', 'warning', 'info'
    description TEXT,
    suggested_fix TEXT,
    field_name VARCHAR(100),
    current_value TEXT,
    expected_value TEXT,
    confidence_score DECIMAL(3,2)
);

-- KPI Storage
CREATE TABLE kpis (
    id SERIAL PRIMARY KEY,
    analysis_id INTEGER REFERENCES analyses(id),
    kpi_name VARCHAR(100),
    kpi_category VARCHAR(50), -- 'efficiency', 'quality', 'performance'
    value DECIMAL(15,4),
    unit VARCHAR(20),
    benchmark_value DECIMAL(15,4),
    trend VARCHAR(20), -- 'improving', 'declining', 'stable'
    calculation_method TEXT
);

-- AI-Generated Insights
CREATE TABLE ai_insights (
    id SERIAL PRIMARY KEY,
    analysis_id INTEGER REFERENCES analyses(id),
    insight_type VARCHAR(50), -- 'summary', 'recommendation', 'alert'
    title VARCHAR(200),
    content TEXT,
    confidence_score DECIMAL(3,2),
    priority VARCHAR(20), -- 'high', 'medium', 'low'
    created_at TIMESTAMP DEFAULT NOW()
);
```

---

## 🔍 Core Analysis Modules

### 1. Data Quality Engine
```python
class DataQualityAnalyzer:
    def __init__(self):
        self.validators = [
            self.validate_temporal_consistency,
            self.validate_physical_constraints,
            self.validate_data_completeness,
            self.detect_outliers,
            self.validate_business_rules
        ]
    
    def validate_temporal_consistency(self, df):
        """Detect impossible time sequences"""
        issues = []
        
        # Departure before arrival
        invalid_departure = df[df['departure_time'] < df['arrival_time']]
        for idx, row in invalid_departure.iterrows():
            issues.append({
                'order_id': row['order_id'],
                'issue_type': 'temporal_inconsistency',
                'severity': 'critical',
                'description': f'Departure time ({row["departure_time"]}) before arrival time ({row["arrival_time"]})',
                'suggested_fix': 'Verify and correct time entries in source system'
            })
        
        # Pumping start before arrival
        invalid_pumping = df[df['pumping_start_time'] < df['arrival_time']]
        # ... similar logic
        
        return issues
    
    def validate_physical_constraints(self, df):
        """Check for physically impossible values"""
        issues = []
        
        # Impossible transfer rates (e.g., > 1000 T/H for typical operations)
        high_rates = df[df['transfer_rate_th'] > 1000]
        # Negative weights
        negative_weights = df[df['weight_tons'] < 0]
        # Negative durations
        negative_durations = df[
            (df['terminal_time_min'] < 0) | 
            (df['pump_time_min'] < 0) |
            (df['prepump_time_min'] < 0)
        ]
        
        return issues
    
    def detect_user_vs_system_patterns(self, df):
        """Identify likely user input errors vs system-generated data"""
        # Round numbers often indicate manual entry
        # Specific time patterns (e.g., exactly on the hour) may indicate manual entry
        # Inconsistent granularity in timestamps
        pass
```

### 2. KPI Calculation Engine
```python
class KPICalculator:
    def calculate_efficiency_metrics(self, df):
        """Calculate operational efficiency KPIs"""
        return {
            'terminal_efficiency': self.calculate_terminal_efficiency(df),
            'pumping_efficiency': self.calculate_pumping_efficiency(df),
            'turnaround_time': self.calculate_average_turnaround(df),
            'capacity_utilization': self.calculate_capacity_utilization(df)
        }
    
    def calculate_terminal_efficiency(self, df):
        """Terminal time vs. benchmark analysis"""
        # Industry benchmark for terminal time per ton
        benchmark_time_per_ton = 0.5  # hours per ton
        
        df['actual_time_per_ton'] = df['terminal_time_min'] / 60 / df['weight_tons']
        efficiency = (benchmark_time_per_ton / df['actual_time_per_ton'].mean()) * 100
        
        return {
            'score': min(efficiency, 100),
            'trend': self.calculate_trend(df, 'actual_time_per_ton'),
            'benchmark': benchmark_time_per_ton,
            'actual': df['actual_time_per_ton'].mean()
        }
    
    def calculate_data_quality_score(self, df, issues):
        """Overall data quality percentage"""
        total_records = len(df)
        critical_issues = len([i for i in issues if i['severity'] == 'critical'])
        warning_issues = len([i for i in issues if i['severity'] == 'warning'])
        
        # Weighted scoring: critical issues have more impact
        penalty = (critical_issues * 2) + (warning_issues * 1)
        max_possible_penalty = total_records * 2
        
        quality_score = max(0, ((max_possible_penalty - penalty) / max_possible_penalty) * 100)
        return quality_score
```

### 3. LLM Integration Service
```python
class InsightGenerator:
    def __init__(self, llm_client):
        self.llm_client = llm_client
        
    async def generate_executive_summary(self, analysis_results):
        """Generate natural language summary for non-technical users"""
        prompt = f"""
        You are analyzing operations data for a logistics terminal. Please provide an executive summary based on this analysis:
        
        Data Overview:
        - Total orders processed: {analysis_results['total_orders']}
        - Date range: {analysis_results['date_range']}
        - Data quality score: {analysis_results['quality_score']:.1f}%
        
        Key Issues Found:
        {self._format_issues_for_prompt(analysis_results['issues'])}
        
        Performance Metrics:
        {self._format_kpis_for_prompt(analysis_results['kpis'])}
        
        Please provide:
        1. A 2-3 sentence executive summary in plain English
        2. Top 3 priority areas for improvement
        3. Specific actionable recommendations with expected impact
        4. Any immediate risks or concerns that need attention
        
        Keep the language accessible for operations managers without technical background.
        """
        
        response = await self.llm_client.generate(prompt)
        return response
    
    async def generate_specific_recommendations(self, issue):
        """Generate detailed recommendations for specific issues"""
        prompt = f"""
        Issue Details:
        - Type: {issue['issue_type']}
        - Order ID: {issue['order_id']}
        - Description: {issue['description']}
        - Current Value: {issue['current_value']}
        
        Provide specific, actionable recommendations to:
        1. Fix this specific issue
        2. Prevent similar issues in the future
        3. Improve the underlying process
        
        Include estimated effort and potential impact.
        """
        
        return await self.llm_client.generate(prompt)
```

---

## 🎨 UI/UX Design Guidelines - EVOS Style

### Design Philosophy
Following the modern, professional aesthetic of evos.eu, the application should embody:
- **Clean minimalism** with purposeful white space
- **Professional European design** sensibilities
- **Industrial-grade reliability** reflected in visual choices
- **Subtle sophistication** over flashy elements
- **Data-first approach** with clear hierarchy

### Color Palette & Visual Identity

Note: This section is updated to reflect a cleaner, more modern EVOS corporate look. It supersedes any previous style notes.

**Primary Color Scheme:**
```css
/* EVOS-inspired Professional Palette */
:root {
  /* Primary Blues - Trust & Reliability */
  --primary-blue: #1e40af;        /* EVOS blue */
  --primary-blue-light: #3b82f6;  /* Interactive blue */
  --primary-blue-subtle: #eff6ff; /* Background tint */
  
  /* Secondary Greens - Operations & Success */
  --success-green: #059669;         /* Success indicators */
  --success-green-light: #10b981;   /* Positive trends */
  --success-green-subtle: #ecfdf5;  /* Success backgrounds */
  
  /* Accent Colors - Status & Alerts */
  --warning-amber: #d97706;         /* Attention needed */
  --error-red: #dc2626;             /* Critical issues */
  --info-slate: #475569;            /* Neutral information */
  
  /* Neutral Foundation */
  --neutral-white: #ffffff;         /* Pure backgrounds */
  --neutral-gray-50: #f8fafc;      /* Light backgrounds */
  --neutral-gray-100: #f1f5f9;     /* Card backgrounds */
  --neutral-gray-200: #e2e8f0;     /* Borders */
  --neutral-gray-500: #64748b;     /* Secondary text */
  --neutral-gray-900: #0f172a;     /* Primary text */
}
```

**Industrial Accent Colors:**
```css
/* Operations-Specific Accents */
--terminal-blue: #0ea5e9;      /* Terminal/logistics blue */
--cargo-orange: #ea580c;       /* Cargo/movement indicators */
--steel-gray: #374151;         /* Industrial elements */
--safety-yellow: #fbbf24;      /* Safety/caution elements */
```

### Typography System

**Font Stack (Modern European Professional):**
```css
/* Primary: Clean, readable sans-serif */
--font-primary: 'Inter', 'Segoe UI', 'Roboto', sans-serif;

/* Data/Metrics: Monospace for numbers */
--font-mono: 'JetBrains Mono', 'SF Mono', 'Monaco', monospace;

/* Headings: Slightly more character */
--font-headings: 'Poppins', 'Inter', sans-serif;
```

**Typography Scale:**
```css
/* Text Sizes - Professional Scale */
--text-xs: 0.75rem;    /* 12px - Small labels */
--text-sm: 0.875rem;   /* 14px - Secondary text */
--text-base: 1rem;     /* 16px - Body text */
--text-lg: 1.125rem;   /* 18px - Emphasized text */
--text-xl: 1.25rem;    /* 20px - Section headers */
--text-2xl: 1.5rem;    /* 24px - Page titles */
--text-3xl: 1.875rem;  /* 30px - Dashboard headers */

/* Font Weights */
--font-light: 300;
--font-normal: 400;
--font-medium: 500;
--font-semibold: 600;
--font-bold: 700;
```

### Layout & Spacing System

**EVOS-Inspired Layout Principles:**
- **Generous white space** for clarity and focus
- **Card-based design** for data organization
- **Consistent 8px grid system** for alignment
- **Progressive disclosure** of information
- **Clear visual hierarchy** with proper contrast

**Spacing Scale:**
```css
/* Spacing System - 8px Base Grid */
--space-1: 0.25rem;   /* 4px */
--space-2: 0.5rem;    /* 8px */
--space-3: 0.75rem;   /* 12px */
--space-4: 1rem;      /* 16px */
--space-6: 1.5rem;    /* 24px */
--space-8: 2rem;      /* 32px */
--space-12: 3rem;     /* 48px */
--space-16: 4rem;     /* 64px */
```

### Component Design Language

**Cards & Containers:**
```css
.evos-card {
  background: var(--neutral-white);
  border: 1px solid var(--neutral-gray-200);
  border-radius: 12px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
  padding: var(--space-6);
  transition: all 0.2s ease;
}

.evos-card:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
  transform: translateY(-1px);
}
```

**Buttons (Professional & Actionable):**
```css
.btn-primary {
  background: var(--primary-blue);
  color: var(--neutral-white);
  border-radius: 8px;
  padding: var(--space-3) var(--space-6);
  font-weight: var(--font-medium);
  transition: all 0.15s ease;
}

.btn-primary:hover {
  background: var(--primary-blue-light);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(30, 64, 175, 0.18);
}

.btn-ghost {
  background: transparent;
  color: var(--primary-blue);
  border: 1px solid var(--neutral-gray-200);
  border-radius: 8px;
  padding: var(--space-3) var(--space-6);
}
```

**Data Visualizations:**
- **Subtle gradients** for depth without distraction
- **Rounded corners** (8-12px) for modern feel
- **Consistent color coding** across all charts
- **Clear data labels** with proper contrast
- **Interactive elements** with smooth transitions

### Dashboard Layout Specifications (Refined)

**Header Design:**
- **Height:** 64px
- **Background:** Clean white with subtle shadow
- **Logo placement:** Left-aligned with proper breathing room
- **Navigation:** Minimal, icon-based with clear labels
- **User profile:** Right-aligned with professional styling

**Sidebar (Optional):**
- **Width:** 240px (collapsed: 64px)
- **Background:** Light gray (--neutral-gray-50)
- **Navigation:** Clean icons with hover states
- **Collapsible:** Smooth animations

**Main Content Area:**
```css
.main-content {
  padding: var(--space-6) var(--space-8);
  background: var(--neutral-gray-50);
  min-height: calc(100vh - 64px);
}
```

**KPI Cards Layout:**
```css
.kpi-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: var(--space-6);
  margin-bottom: var(--space-8);
}
```

### Status Indicators & Visual Feedback

**Quality Score Visualization:**
- **Excellent (90-100%):** Green with checkmark icon
- **Good (75-89%):** Light green with trending up
- **Needs Attention (60-74%):** Amber with warning icon
- **Critical (<60%):** Red with alert icon

**Data Quality Issues:**
```css
.issue-critical { 
  border-left: 4px solid var(--error-red);
  background: #fef2f2;
}
.issue-warning { 
  border-left: 4px solid var(--warning-amber);
  background: #fffbeb;
}
.issue-info { 
  border-left: 4px solid var(--info-slate);
  background: #f8fafc;
}
```

### Interactive Elements

**Micro-animations:**
- **Smooth transitions** (0.2s ease) for all interactive elements
- **Subtle hover effects** with slight elevation
- **Loading states** with professional spinners
- **Success animations** for completed actions

**Charts & Graphs:**
- **Consistent color palette** across all visualizations
- **Interactive tooltips** with relevant context
- **Smooth data transitions** when filtering
- **Responsive design** for all screen sizes

### Mobile-First Responsive Design

**Breakpoints:**
```css
/* Mobile First Approach */
--mobile: 0px;           /* 0-767px */
--tablet: 768px;         /* 768-1023px */
--desktop: 1024px;       /* 1024-1439px */
--large-desktop: 1440px; /* 1440px+ */
```

**Mobile Optimizations:**
- **Touch-friendly** button sizes (44px minimum)
- **Swipe gestures** for card navigation
- **Collapsible** sections for complex data
- **Progressive enhancement** from mobile up

### Accessibility Standards

**WCAG 2.1 AA Compliance:**
- **Contrast ratios:** Minimum 4.5:1 for normal text
- **Focus indicators:** Clear, high-contrast outlines
- **Keyboard navigation:** Full accessibility without mouse
- **Screen reader support:** Proper ARIA labels
- **Color independence:** Information not conveyed by color alone

---

## 🖥️ Frontend Components Architecture

### Dashboard Layout
```tsx
// pages/dashboard.tsx
import { DashboardLayout } from '@/components/layouts/DashboardLayout';
import { KPICards } from '@/components/dashboard/KPICards';
import { TrendChart } from '@/components/charts/TrendChart';
import { RecentAnalyses } from '@/components/dashboard/RecentAnalyses';
import { AIInsights } from '@/components/ai/AIInsights';

export default function Dashboard() {
  return (
    <DashboardLayout>
      <div className="grid grid-cols-12 gap-6">
        {/* KPI Overview */}
        <div className="col-span-12">
          <KPICards />
        </div>
        
        {/* Main Chart Area */}
        <div className="col-span-8">
          <TrendChart />
        </div>
        
        {/* Sidebar */}
        <div className="col-span-4 space-y-6">
          <RecentAnalyses />
          <AIInsights />
        </div>
      </div>
    </DashboardLayout>
  );
}
```

### File Upload Component
```tsx
// components/upload/FileUpload.tsx
import { useDropzone } from 'react-dropzone';
import { useState } from 'react';

export function FileUpload({ onUploadComplete }: { onUploadComplete: (analysisId: string) => void }) {
  const [uploadProgress, setUploadProgress] = useState(0);
  const [isProcessing, setIsProcessing] = useState(false);
  
  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    accept: {
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet': ['.xlsx'],
      'application/vnd.ms-excel': ['.xls'],
    },
    maxFiles: 1,
    onDrop: handleFileUpload,
  });

  const handleFileUpload = async (files: File[]) => {
    const file = files[0];
    setIsProcessing(true);
    
    try {
      // Upload file with progress tracking
      const analysisId = await uploadFileWithProgress(file, setUploadProgress);
      onUploadComplete(analysisId);
    } catch (error) {
      // Handle error
    } finally {
      setIsProcessing(false);
    }
  };

  return (
    <div className="border-2 border-dashed border-gray-300 rounded-lg p-8 text-center">
      <div {...getRootProps()}>
        <input {...getInputProps()} />
        {isDragActive ? (
          <p>Drop the Excel file here...</p>
        ) : (
          <div>
            <p>Drag & drop PowerBI export file, or click to select</p>
            <p className="text-sm text-gray-500">Supports .xlsx and .xls files</p>
          </div>
        )}
      </div>
      
      {isProcessing && (
        <div className="mt-4">
          <div className="bg-gray-200 rounded-full h-2">
            <div 
              className="bg-blue-600 h-2 rounded-full transition-all duration-300"
              style={{ width: `${uploadProgress}%` }}
            />
          </div>
          <p className="text-sm mt-2">Processing... {uploadProgress}%</p>
        </div>
      )}
    </div>
  );
}
```

### Analysis Results Component
```tsx
// components/analysis/AnalysisResults.tsx
export function AnalysisResults({ analysisId }: { analysisId: string }) {
  const { data: analysis, isLoading } = useQuery({
    queryKey: ['analysis', analysisId],
    queryFn: () => fetchAnalysis(analysisId),
  });

  if (isLoading) return <LoadingSpinner />;

  return (
    <div className="space-y-6">
      {/* Quality Score Section */}
      <QualityScoreCard score={analysis.quality_score} trend={analysis.quality_trend} />
      
      {/* Performance Metrics */}
      <PerformanceMetrics kpis={analysis.kpis} />
      
      {/* Issues Overview */}
      <IssuesOverview issues={analysis.issues} />
      
      {/* AI Insights */}
      <AIInsightsPanel insights={analysis.ai_insights} />
    </div>
  );
}
```

---

## 🚀 Implementation Roadmap

### Phase 1: MVP (4-6 weeks)
**Week 1-2: Foundation**
- [ ] Set up development environment
- [ ] Database schema implementation
- [ ] Basic file upload functionality
- [ ] Excel parsing and data extraction

**Week 3-4: Core Analysis**
- [ ] Basic data quality checks implementation
- [ ] KPI calculation engine
- [ ] Simple dashboard with key metrics
- [ ] Data storage and retrieval

**Week 5-6: User Interface**
- [ ] Dashboard UI implementation
- [ ] File upload interface
- [ ] Basic analysis results display
- [ ] Historical data viewing

### Phase 2: Enhanced Features (6-8 weeks)
**Week 7-10: Advanced Analysis**
- [ ] Statistical anomaly detection
- [ ] Advanced data quality algorithms
- [ ] Trend analysis and benchmarking
- [ ] Performance comparison features

**Week 11-14: AI Integration**
- [ ] LLM service integration
- [ ] Natural language insight generation
- [ ] Recommendation engine
- [ ] Interactive AI chat interface

### Phase 3: Production Ready (4-6 weeks)
**Week 15-18: Polish & Performance**
- [ ] Performance optimization
- [ ] Advanced visualizations
- [ ] User management and permissions
- [ ] API documentation

**Week 19-20: Deployment**
- [ ] Production deployment setup
- [ ] Monitoring and logging
- [ ] User training and documentation
- [ ] Performance testing

---

## 🔧 Development Guidelines

### Code Quality Standards
- **TypeScript** for type safety
- **ESLint + Prettier** for code formatting
- **Jest + Testing Library** for testing
- **Conventional Commits** for version control
- **API documentation** with OpenAPI/Swagger

### Performance Considerations
- **Lazy loading** for large datasets
- **Virtual scrolling** for data tables
- **Background processing** for file uploads
- **Caching strategy** for frequent queries
- **Database indexing** for time-series queries

### Security Requirements
- **File upload validation** and sanitization
- **SQL injection prevention**
- **CORS configuration**
- **Authentication and authorization**
- **Data encryption** for sensitive information

---

This specification provides a comprehensive foundation for building a robust operations data analysis application that will transform how your team handles PowerBI exports and operational insights.
