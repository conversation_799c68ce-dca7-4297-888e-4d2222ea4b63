from datetime import datetime
from sqlalchemy import Column, DateTime, Integer, String, BigInteger
from app.db.session import Base


class Upload(Base):
    __tablename__ = "uploads"

    id = Column(Integer, primary_key=True, index=True)
    filename = Column(String(255), nullable=False)
    stored_path = Column(String(500), nullable=False)
    upload_date = Column(DateTime, default=datetime.utcnow)
    status = Column(String(50), default="processing")
    file_size_bytes = Column(BigInteger)
    row_count = Column(Integer)

