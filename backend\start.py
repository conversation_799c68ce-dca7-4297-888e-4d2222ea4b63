import os
import sys
from typing import Optional

import uvicorn

# Ensure imports work when running as a script
CURRENT_DIR = os.path.dirname(os.path.abspath(__file__))
if CURRENT_DIR not in sys.path:
    sys.path.insert(0, CURRENT_DIR)

from app.core.config import settings  # noqa: E402
from app.db.init_db import init as init_db  # noqa: E402


def main(port: Optional[int] = None) -> None:
    # Initialize DB (creates tables if missing)
    init_db()

    # Determine port
    port_val = port or int(os.getenv("PORT", "8000"))

    # Run API
    uvicorn.run(
        "app.main:app",
        host="0.0.0.0",
        port=port_val,
        reload=os.getenv("APP_ENV", "development") == "development",
    )


if __name__ == "__main__":
    # Optional CLI arg: port
    arg_port = int(sys.argv[1]) if len(sys.argv) > 1 else None
    main(arg_port)

