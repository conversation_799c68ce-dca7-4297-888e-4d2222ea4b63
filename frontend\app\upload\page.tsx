"use client"
import React, { useState } from 'react'

const API_BASE = process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:8000'

export default function UploadPage() {
  const [file, setFile] = useState<File | null>(null)
  const [status, setStatus] = useState<string>('')

  const onSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    if (!file) return
    const form = new FormData()
    form.append('file', file)
    setStatus('Uploading…')
    try {
      const res = await fetch(`${API_BASE}/uploads`, { method: 'POST', body: form })
      const data = await res.json()
      if (!res.ok) throw new Error(data?.detail || 'Upload failed')
      setStatus(`Queued upload ${data.upload_id}`)
    } catch (e: any) {
      setStatus(`Error: ${e.message}`)
    }
  }

  return (
    <main className="space-y-6">
      <div className="evos-card">
        <h2 className="text-lg font-medium mb-4">Upload Excel</h2>
        <form onSubmit={onSubmit} className="space-y-4">
          <input
            type="file"
            accept=".xlsx,.xls"
            onChange={(e) => setFile(e.target.files?.[0] || null)}
            className="block"
          />
          <button type="submit" className="bg-primary text-white rounded-lg px-4 py-2">Upload</button>
        </form>
        {status && <div className="mt-4 text-slate">{status}</div>}
      </div>
    </main>
  )
}

