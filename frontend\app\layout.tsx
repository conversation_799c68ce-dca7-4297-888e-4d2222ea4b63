import './globals.css'
import React from 'react'

export const metadata = {
  title: 'Operations Intelligence',
  description: 'EVOS-style operations analytics',
}

export default function RootLayout({ children }: { children: React.ReactNode }) {
  return (
    <html lang="en">
      <body className="min-h-screen bg-primary-subtle">
        <div className="max-w-6xl mx-auto p-6">
          <header className="mb-6 flex items-center justify-between">
            <h1 className="text-2xl font-semibold text-primary">Operations Intelligence</h1>
            <nav className="space-x-4">
              <a href="/" className="text-slate hover:underline">Dashboard</a>
              <a href="/upload" className="text-slate hover:underline">Upload</a>
            </nav>
          </header>
          {children}
        </div>
      </body>
    </html>
  )
}

