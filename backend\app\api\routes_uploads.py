from datetime import datetime
from pathlib import Path
from typing import Optional

from fastapi import APIRouter, BackgroundTasks, File, HTTPException, UploadFile

from app.core.config import settings
from app.db.session import SessionLocal
from app.models.upload import Upload
from app.services.processing import process_upload_background
from app.schemas.upload import UploadOut
from typing import List
from fastapi import Depends
from sqlalchemy.orm import Session


def get_db():
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()


router = APIRouter()


def _validate_extension(filename: str) -> None:
    allowed = {".xlsx", ".xls"}
    ext = Path(filename).suffix.lower()
    if ext not in allowed:
        raise HTTPException(status_code=400, detail=f"Unsupported file type: {ext}")


@router.post("/uploads")
async def create_upload(background_tasks: BackgroundTasks, file: UploadFile = File(...)):
    _validate_extension(file.filename)

    # Store to local filesystem
    dest_dir = Path(settings.UPLOAD_DIR)
    dest_dir.mkdir(parents=True, exist_ok=True)
    timestamp = datetime.utcnow().strftime("%Y%m%dT%H%M%S%fZ")
    safe_name = f"{timestamp}__{Path(file.filename).name}"
    dest_path = dest_dir / safe_name

    content = await file.read()
    max_bytes = settings.MAX_UPLOAD_MB * 1024 * 1024
    if len(content) > max_bytes:
        raise HTTPException(status_code=413, detail="File too large")

    dest_path.write_bytes(content)

    # Record in DB
    db = SessionLocal()
    try:
        upload = Upload(
            filename=file.filename,
            stored_path=str(dest_path),
            status="processing",
            file_size_bytes=len(content),
        )
        db.add(upload)
        db.commit()
        db.refresh(upload)
    finally:
        db.close()

    # Background processing (no Celery required in dev)
    background_tasks.add_task(process_upload_background, upload_id=upload.id, file_path=str(dest_path))

    return {"upload_id": upload.id, "filename": file.filename, "status": "queued"}


@router.get("/uploads", response_model=List[UploadOut])
def list_uploads(db: Session = Depends(get_db)):
    return db.query(Upload).order_by(Upload.upload_date.desc()).all()


@router.get("/uploads/{upload_id}", response_model=UploadOut)
def get_upload(upload_id: int, db: Session = Depends(get_db)):
    upload = db.get(Upload, upload_id)
    if not upload:
        raise HTTPException(status_code=404, detail="Upload not found")
    return upload
