from datetime import datetime
from typing import List, Optional
from pydantic import BaseModel


class KPIOut(BaseModel):
    id: int
    kpi_name: str
    kpi_category: Optional[str] = None
    value: Optional[float] = None
    unit: Optional[str] = None
    benchmark_value: Optional[float] = None
    trend: Optional[str] = None
    calculation_method: Optional[str] = None

    class Config:
        from_attributes = True


class IssueOut(BaseModel):
    id: int
    order_id: Optional[str] = None
    issue_type: Optional[str] = None
    severity: Optional[str] = None
    description: Optional[str] = None
    suggested_fix: Optional[str] = None
    field_name: Optional[str] = None
    current_value: Optional[str] = None
    expected_value: Optional[str] = None
    confidence_score: Optional[float] = None

    class Config:
        from_attributes = True


class AnalysisOut(BaseModel):
    id: int
    upload_id: int
    analysis_date: datetime
    total_records: Optional[int] = None
    quality_score: Optional[float] = None
    performance_score: Optional[float] = None
    error_count: Optional[int] = None
    warning_count: Optional[int] = None
    analysis_summary: Optional[str] = None
    issues: List[IssueOut] = []
    kpis: List[KPIOut] = []

    class Config:
        from_attributes = True

