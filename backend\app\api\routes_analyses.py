from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.orm import Session

from app.db.session import SessionLocal
from app.models.analysis import Analysis
from app.schemas.analysis import AnalysisOut


router = APIRouter()


def get_db():
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()


@router.get("/uploads/{upload_id}/analysis", response_model=AnalysisOut)
def get_analysis_for_upload(upload_id: int, db: Session = Depends(get_db)):
    analysis = db.query(Analysis).filter(Analysis.upload_id == upload_id).order_by(Analysis.id.desc()).first()
    if not analysis:
        raise HTTPException(status_code=404, detail="Analysis not found or not completed")
    # relationships issues and kpis are loaded lazily if accessed; ensure access for serialization
    _ = analysis.issues  # touch
    _ = analysis.kpis
    return analysis

