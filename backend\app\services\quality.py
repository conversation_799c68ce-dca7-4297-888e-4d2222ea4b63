from typing import List, Dict


def analyze_quality_stub(df) -> List[Dict]:
    """Very basic checks reflecting the spec; replace with full engine.
    Returns a list of issue dicts.
    """
    issues: List[Dict] = []
    # Temporal inconsistencies example
    for col in ["arrival_time", "departure_time", "pumping_start_time", "pumping_end_time"]:
        if col not in df.columns:
            continue

    if all(c in df.columns for c in ["arrival_time", "departure_time"]):
        mask = df["departure_time"] < df["arrival_time"]
        for _, row in df[mask].iterrows():
            issues.append({
                "issue_type": "temporal_inconsistency",
                "severity": "critical",
                "description": "Departure before arrival",
                "order_id": row.get("order_id", None),
            })

    # Physical impossibilities
    if "weight_tons" in df.columns:
        neg = df[df["weight_tons"] < 0]
        for _, row in neg.iterrows():
            issues.append({
                "issue_type": "negative_weight",
                "severity": "warning",
                "description": "Negative weight_tons",
                "order_id": row.get("order_id", None),
            })

    return issues

