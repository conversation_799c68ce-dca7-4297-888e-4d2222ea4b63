from fastapi import APIRouter
from app.core.config import settings

router = APIRouter()


@router.get("/health")
def health():
    return {
        "status": "ok",
        "app": settings.APP_NAME,
        "env": settings.APP_ENV,
        "config": {
            "storage": settings.FILE_STORAGE,
            "upload_dir": settings.UPLOAD_DIR,
            "celery_enabled": settings.ENABLE_CELERY,
            "redis_enabled": settings.ENABLE_REDIS,
            "llm_provider": settings.LLM_PROVIDER,
            "max_upload_mb": settings.MAX_UPLOAD_MB,
            "db": "sqlite" if settings.DATABASE_URL.startswith("sqlite") else "external",
        },
    }

