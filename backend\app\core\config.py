import os
from pathlib import Path
from pydantic_settings import BaseSettings
from dotenv import load_dotenv

# Load env from repo root and backend/.env (backend overrides root if both set)
ROOT_ENV = Path(__file__).resolve().parents[2] / ".env"
BACKEND_ENV = Path(__file__).resolve().parents[1] / ".env"
if ROOT_ENV.exists():
    load_dotenv(ROOT_ENV, override=False)
if BACKEND_ENV.exists():
    load_dotenv(BACKEND_ENV, override=True)


class Settings(BaseSettings):
    APP_NAME: str = "operations-intelligence-backend"
    APP_ENV: str = os.getenv("APP_ENV", "development")

    DATABASE_URL: str = os.getenv("DATABASE_URL", "sqlite:///./dev.db")

    FILE_STORAGE: str = os.getenv("FILE_STORAGE", "local")
    UPLOAD_DIR: str = os.getenv("UPLOAD_DIR", "storage/uploads")
    MAX_UPLOAD_MB: int = int(os.getenv("MAX_UPLOAD_MB", "50"))

    ENABLE_CELERY: bool = os.getenv("ENABLE_CELERY", "false").lower() == "true"
    ENABLE_REDIS: bool = os.getenv("ENABLE_REDIS", "false").lower() == "true"

    # LLM configuration (OpenRouter-ready)
    LLM_PROVIDER: str = os.getenv("LLM_PROVIDER", "openrouter")
    OPENROUTER_API_KEY: str | None = os.getenv("OPENROUTER_API_KEY")
    OPENROUTER_MODEL: str = os.getenv("OPENROUTER_MODEL", "openrouter/openai/gpt-4o-mini")
    OPENROUTER_BASE_URL: str = os.getenv("OPENROUTER_BASE_URL", "https://openrouter.ai/api/v1")

    class Config:
        env_file = ".env"


settings = Settings()

# Ensure upload directory exists for local storage
if settings.FILE_STORAGE == "local":
    Path(settings.UPLOAD_DIR).mkdir(parents=True, exist_ok=True)
