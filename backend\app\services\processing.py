import logging
from pathlib import Path

from app.db.session import SessionLocal
from app.models.upload import Upload
from app.models.analysis import Analysis, DataQualityIssue, KPI
from app.services.quality import analyze_quality_stub
from app.services.kpi import compute_kpis_stub

logger = logging.getLogger(__name__)


def process_upload_background(upload_id: int, file_path: str) -> None:
    db = SessionLocal()
    try:
        upload = db.get(Upload, upload_id)
        if not upload:
            logger.error("Upload id %s not found", upload_id)
            return

        # Try to parse Excel with pandas; if unavailable, log and mark as completed with no analysis
        try:
            import pandas as pd  # type: ignore
        except Exception as e:
            logger.warning("pandas not available; creating minimal analysis stub: %s", e)
            # Create a minimal analysis so the UI can render something
            analysis = Analysis(
                upload_id=upload.id,
                total_records=None,
                quality_score=None,
                performance_score=None,
                error_count=0,
                warning_count=0,
                analysis_summary="Analysis requires pandas/openpyxl to parse Excel in this environment.",
            )
            db.add(analysis)
            upload.status = "completed"
            db.add(upload)
            db.commit()
            return

        try:
            df = pd.read_excel(Path(file_path))
            upload.row_count = int(len(df))

            # Basic stubs – replace with real logic later
            issues = analyze_quality_stub(df)
            kpis = compute_kpis_stub(df)

            # Compute simple scores
            total_records = int(len(df))
            critical = sum(1 for i in issues if i.get("severity") == "critical")
            warnings = sum(1 for i in issues if i.get("severity") == "warning")
            penalty = (critical * 2) + (warnings * 1)
            max_penalty = max(1, total_records * 2)
            quality_score = max(0.0, ((max_penalty - penalty) / max_penalty) * 100.0)

            # Persist analysis summary
            analysis = Analysis(
                upload_id=upload.id,
                total_records=total_records,
                quality_score=quality_score,
                performance_score=None,
                error_count=critical,
                warning_count=warnings,
                analysis_summary="Auto-generated summary (stub)",
            )
            db.add(analysis)
            db.flush()  # get analysis.id

            # Persist issues
            for i in issues:
                db.add(
                    DataQualityIssue(
                        analysis_id=analysis.id,
                        order_id=i.get("order_id"),
                        issue_type=i.get("issue_type"),
                        severity=i.get("severity"),
                        description=i.get("description"),
                        suggested_fix=i.get("suggested_fix"),
                        field_name=i.get("field_name"),
                        current_value=i.get("current_value"),
                        expected_value=i.get("expected_value"),
                        confidence_score=i.get("confidence_score"),
                    )
                )

            # Persist KPIs (only those with value present)
            for name, val in kpis.items():
                if val is None:
                    continue
                if isinstance(val, dict):
                    db.add(
                        KPI(
                            analysis_id=analysis.id,
                            kpi_name=name,
                            kpi_category=val.get("category"),
                            value=val.get("actual") or val.get("value"),
                            unit=val.get("unit"),
                            benchmark_value=val.get("benchmark"),
                            trend=val.get("trend"),
                            calculation_method=val.get("calculation_method"),
                        )
                    )
                else:
                    db.add(
                        KPI(
                            analysis_id=analysis.id,
                            kpi_name=name,
                            value=val,
                        )
                    )

            upload.status = "completed"
            db.add(upload)
            db.commit()
            logger.info(
                "Processed upload %s: rows=%s issues=%s quality=%.1f",
                upload_id,
                upload.row_count,
                len(issues),
                quality_score,
            )
        except Exception as e:
            upload.status = "failed"
            db.add(upload)
            db.commit()
            logger.exception("Processing failed for upload %s: %s", upload_id, e)
    finally:
        db.close()
