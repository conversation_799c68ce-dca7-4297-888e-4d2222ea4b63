#Requires -Version 5.1
param(
    [string]$PythonExe = "python",
    [string]$Port = "8000"
)

Write-Host "[dev] Creating venv if missing..."
if (-not (Test-Path ".venv")) {
  try { & $PythonExe -m venv .venv } catch { Write-Host "[dev] Failed to create venv with $PythonExe"; throw }
}

Write-Host "[dev] Activating venv..."
& .\.venv\Scripts\Activate.ps1

Write-Host "[dev] Installing requirements..."
pip install -r backend/requirements.txt

Write-Host "[dev] Initializing DB..."
python -m app.db.init_db

Write-Host "[dev] Starting API on port $Port ..."
uvicorn app.main:app --reload --port $Port
