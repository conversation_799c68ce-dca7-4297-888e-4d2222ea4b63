from datetime import datetime
from sqlalchemy import Column, DateTime, ForeignKey, Integer, Numeric, String, Text
from sqlalchemy.orm import relationship

from app.db.session import Base


class Analysis(Base):
    __tablename__ = "analyses"

    id = Column(Integer, primary_key=True, index=True)
    upload_id = Column(Integer, ForeignKey("uploads.id"), nullable=False, index=True)
    analysis_date = Column(DateTime, default=datetime.utcnow)
    total_records = Column(Integer)
    quality_score = Column(Numeric(5, 2))
    performance_score = Column(Numeric(5, 2))
    error_count = Column(Integer)
    warning_count = Column(Integer)
    analysis_summary = Column(Text)

    issues = relationship("DataQualityIssue", back_populates="analysis", cascade="all, delete-orphan")
    kpis = relationship("KPI", back_populates="analysis", cascade="all, delete-orphan")


class DataQualityIssue(Base):
    __tablename__ = "data_quality_issues"

    id = Column(Integer, primary_key=True)
    analysis_id = Column(Integer, ForeignKey("analyses.id"), index=True, nullable=False)
    order_id = Column(String(100))
    issue_type = Column(String(100))
    severity = Column(String(20))  # 'critical', 'warning', 'info'
    description = Column(Text)
    suggested_fix = Column(Text)
    field_name = Column(String(100))
    current_value = Column(Text)
    expected_value = Column(Text)
    confidence_score = Column(Numeric(3, 2))

    analysis = relationship("Analysis", back_populates="issues")


class KPI(Base):
    __tablename__ = "kpis"

    id = Column(Integer, primary_key=True)
    analysis_id = Column(Integer, ForeignKey("analyses.id"), index=True, nullable=False)
    kpi_name = Column(String(100))
    kpi_category = Column(String(50))  # 'efficiency', 'quality', 'performance'
    value = Column(Numeric(15, 4))
    unit = Column(String(20))
    benchmark_value = Column(Numeric(15, 4))
    trend = Column(String(20))  # 'improving', 'declining', 'stable'
    calculation_method = Column(Text)

    analysis = relationship("Analysis", back_populates="kpis")

