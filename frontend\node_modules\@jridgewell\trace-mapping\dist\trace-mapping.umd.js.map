{"version": 3, "sources": ["umd:@jridgewell/sourcemap-codec", "umd:@j<PERSON>well/resolve-uri", "../src/trace-mapping.ts", "../src/resolve.ts", "../src/strip-filename.ts", "../src/sourcemap-segment.ts", "../src/sort.ts", "../src/by-source.ts", "../src/binary-search.ts", "../src/types.ts", "../src/flatten-map.ts"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AAAA,6CAAAA,SAAA;AAAA,IAAAA,QAAO,UAAU;AAAA;AAAA;;;ACAjB;AAAA,yCAAAC,SAAA;AAAA,IAAAA,QAAO,UAAU;AAAA;AAAA;;;ACAjB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,6BAA+B;;;ACA/B,yBAAuB;;;ACGR,SAAR,cAA+B,MAAyC;AAC7E,MAAI,CAAC,KAAM,QAAO;AAClB,QAAM,QAAQ,KAAK,YAAY,GAAG;AAClC,SAAO,KAAK,MAAM,GAAG,QAAQ,CAAC;AAChC;;;ADHe,SAAR,SACL,QACA,YACS;AACT,QAAM,OAAO,cAAc,MAAM;AAIjC,QAAM,SAAS,aAAa,aAAa,MAAM;AAE/C,SAAO,CAAC,eAAW,mBAAAC,SAAW,UAAU,UAAU,KAAK,IAAI;AAC7D;;;AEAO,IAAM,SAAS;AACf,IAAM,gBAAgB;AACtB,IAAM,cAAc;AACpB,IAAM,gBAAgB;AACtB,IAAM,cAAc;AAEpB,IAAM,qBAAqB;AAC3B,IAAM,uBAAuB;;;AClBrB,SAAR,UACL,UACA,OACsB;AACtB,QAAM,gBAAgB,wBAAwB,UAAU,CAAC;AACzD,MAAI,kBAAkB,SAAS,OAAQ,QAAO;AAI9C,MAAI,CAAC,MAAO,YAAW,SAAS,MAAM;AAEtC,WAAS,IAAI,eAAe,IAAI,SAAS,QAAQ,IAAI,wBAAwB,UAAU,IAAI,CAAC,GAAG;AAC7F,aAAS,CAAC,IAAI,aAAa,SAAS,CAAC,GAAG,KAAK;AAAA,EAC/C;AACA,SAAO;AACT;AAEA,SAAS,wBAAwB,UAAgC,OAAuB;AACtF,WAAS,IAAI,OAAO,IAAI,SAAS,QAAQ,KAAK;AAC5C,QAAI,CAAC,SAAS,SAAS,CAAC,CAAC,EAAG,QAAO;AAAA,EACrC;AACA,SAAO,SAAS;AAClB;AAEA,SAAS,SAAS,MAAmC;AACnD,WAAS,IAAI,GAAG,IAAI,KAAK,QAAQ,KAAK;AACpC,QAAI,KAAK,CAAC,EAAE,MAAM,IAAI,KAAK,IAAI,CAAC,EAAE,MAAM,GAAG;AACzC,aAAO;AAAA,IACT;AAAA,EACF;AACA,SAAO;AACT;AAEA,SAAS,aAAa,MAA0B,OAAoC;AAClF,MAAI,CAAC,MAAO,QAAO,KAAK,MAAM;AAC9B,SAAO,KAAK,KAAK,cAAc;AACjC;AAEO,SAAS,eAA4D,GAAM,GAAc;AAC9F,SAAO,EAAE,MAAM,IAAI,EAAE,MAAM;AAC7B;;;ACnCe,SAAR,eACL,SACA,OACU;AACV,QAAM,UAAoB,MAAM,IAAI,MAAM,CAAC,CAAC;AAE5C,WAAS,IAAI,GAAG,IAAI,QAAQ,QAAQ,KAAK;AACvC,UAAM,OAAO,QAAQ,CAAC;AACtB,aAAS,IAAI,GAAG,IAAI,KAAK,QAAQ,KAAK;AACpC,YAAM,MAAM,KAAK,CAAC;AAClB,UAAI,IAAI,WAAW,EAAG;AAEtB,YAAMC,eAAc,IAAI,aAAa;AACrC,YAAM,aAAa,IAAI,WAAW;AAClC,YAAM,eAAe,IAAI,aAAa;AAEtC,YAAM,SAAS,QAAQA,YAAW;AAClC,YAAM,OAAQ,4CAAuB,CAAC;AACtC,WAAK,KAAK,CAAC,cAAc,GAAG,IAAI,MAAM,CAAC,CAAC;AAAA,IAC1C;AAAA,EACF;AAEA,WAAS,IAAI,GAAG,IAAI,QAAQ,QAAQ,KAAK;AACvC,UAAM,SAAS,QAAQ,CAAC;AACxB,aAAS,IAAI,GAAG,IAAI,OAAO,QAAQ,KAAK;AACtC,YAAM,OAAO,OAAO,CAAC;AACrB,UAAI,KAAM,MAAK,KAAK,cAAc;AAAA,IACpC;AAAA,EACF;AAEA,SAAO;AACT;;;AC/BO,IAAI,QAAQ;AAkBZ,SAAS,aACd,UACA,QACA,KACA,MACQ;AACR,SAAO,OAAO,MAAM;AAClB,UAAM,MAAM,OAAQ,OAAO,OAAQ;AACnC,UAAM,MAAM,SAAS,GAAG,EAAE,MAAM,IAAI;AAEpC,QAAI,QAAQ,GAAG;AACb,cAAQ;AACR,aAAO;AAAA,IACT;AAEA,QAAI,MAAM,GAAG;AACX,YAAM,MAAM;AAAA,IACd,OAAO;AACL,aAAO,MAAM;AAAA,IACf;AAAA,EACF;AAEA,UAAQ;AACR,SAAO,MAAM;AACf;AAEO,SAAS,WACd,UACA,QACA,OACQ;AACR,WAAS,IAAI,QAAQ,GAAG,IAAI,SAAS,QAAQ,QAAQ,KAAK;AACxD,QAAI,SAAS,CAAC,EAAE,MAAM,MAAM,OAAQ;AAAA,EACtC;AACA,SAAO;AACT;AAEO,SAAS,WACd,UACA,QACA,OACQ;AACR,WAAS,IAAI,QAAQ,GAAG,KAAK,GAAG,QAAQ,KAAK;AAC3C,QAAI,SAAS,CAAC,EAAE,MAAM,MAAM,OAAQ;AAAA,EACtC;AACA,SAAO;AACT;AAEO,SAAS,gBAA2B;AACzC,SAAO;AAAA,IACL,SAAS;AAAA,IACT,YAAY;AAAA,IACZ,WAAW;AAAA,EACb;AACF;AAMO,SAAS,qBACd,UACA,QACA,OACA,KACQ;AACR,QAAM,EAAE,SAAS,YAAY,UAAU,IAAI;AAE3C,MAAI,MAAM;AACV,MAAI,OAAO,SAAS,SAAS;AAC7B,MAAI,QAAQ,SAAS;AACnB,QAAI,WAAW,YAAY;AACzB,cAAQ,cAAc,MAAM,SAAS,SAAS,EAAE,MAAM,MAAM;AAC5D,aAAO;AAAA,IACT;AAEA,QAAI,UAAU,YAAY;AAExB,YAAM,cAAc,KAAK,IAAI;AAAA,IAC/B,OAAO;AACL,aAAO;AAAA,IACT;AAAA,EACF;AACA,QAAM,UAAU;AAChB,QAAM,aAAa;AAEnB,SAAQ,MAAM,YAAY,aAAa,UAAU,QAAQ,KAAK,IAAI;AACpE;;;ACHO,SAAS,MAAS,KAA4B;AACnD,SAAO,OAAO,QAAQ,WAAW,KAAK,MAAM,GAAG,IAAK;AACtD;;;ACvFO,IAAM,aAAyB,SAAU,KAAK,QAAQ;AAC3D,QAAM,SAAS,MAAM,GAA8B;AAEnD,MAAI,EAAE,cAAc,SAAS;AAC3B,WAAO,IAAI,SAAS,QAA2D,MAAM;AAAA,EACvF;AAEA,QAAM,WAAiC,CAAC;AACxC,QAAM,UAAoB,CAAC;AAC3B,QAAM,iBAAoC,CAAC;AAC3C,QAAM,QAAkB,CAAC;AACzB,QAAM,aAAuB,CAAC;AAE9B;AAAA,IACE;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AAEA,QAAM,SAA2B;AAAA,IAC/B,SAAS;AAAA,IACT,MAAM,OAAO;AAAA,IACb;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AAEA,SAAO,oBAAoB,MAAM;AACnC;AAEA,SAAS,QACP,OACA,QACA,UACA,SACA,gBACA,OACA,YACA,YACA,cACA,UACA,YACA;AACA,QAAM,EAAE,SAAS,IAAI;AACrB,WAAS,IAAI,GAAG,IAAI,SAAS,QAAQ,KAAK;AACxC,UAAM,EAAE,KAAK,OAAO,IAAI,SAAS,CAAC;AAElC,QAAI,KAAK;AACT,QAAI,KAAK;AACT,QAAI,IAAI,IAAI,SAAS,QAAQ;AAC3B,YAAM,aAAa,SAAS,IAAI,CAAC,EAAE;AACnC,WAAK,KAAK,IAAI,UAAU,aAAa,WAAW,IAAI;AAEpD,UAAI,OAAO,UAAU;AACnB,aAAK,KAAK,IAAI,YAAY,eAAe,WAAW,MAAM;AAAA,MAC5D,WAAW,KAAK,UAAU;AACxB,aAAK,eAAe,WAAW;AAAA,MACjC;AAAA,IACF;AAEA;AAAA,MACE;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA,aAAa,OAAO;AAAA,MACpB,eAAe,OAAO;AAAA,MACtB;AAAA,MACA;AAAA,IACF;AAAA,EACF;AACF;AAEA,SAAS,WACP,OACA,QACA,UACA,SACA,gBACA,OACA,YACA,YACA,cACA,UACA,YACA;AACA,QAAM,SAAS,MAAM,KAAK;AAC1B,MAAI,cAAc,OAAQ,QAAO,QAAQ,GAAI,SAAmD;AAEhG,QAAM,MAAM,IAAI,SAAS,QAAQ,MAAM;AACvC,QAAM,gBAAgB,QAAQ;AAC9B,QAAM,cAAc,MAAM;AAC1B,QAAM,UAAU,gBAAgB,GAAG;AACnC,QAAM,EAAE,iBAAiB,gBAAgB,UAAU,YAAY,QAAQ,IAAI;AAE3E,SAAO,SAAS,eAAe;AAC/B,SAAO,OAAO,IAAI,KAAK;AAEvB,MAAI,SAAU,QAAO,gBAAgB,QAAQ;AAAA,MACxC,UAAS,IAAI,GAAG,IAAI,gBAAgB,QAAQ,IAAK,gBAAe,KAAK,IAAI;AAE9E,MAAI,QAAS,UAAS,IAAI,GAAG,IAAI,QAAQ,QAAQ,IAAK,YAAW,KAAK,QAAQ,CAAC,IAAI,aAAa;AAEhG,WAAS,IAAI,GAAG,IAAI,QAAQ,QAAQ,KAAK;AACvC,UAAM,QAAQ,aAAa;AAM3B,QAAI,QAAQ,SAAU;AAItB,UAAM,MAAM,QAAQ,UAAU,KAAK;AAGnC,UAAM,UAAU,MAAM,IAAI,eAAe;AAEzC,UAAM,OAAO,QAAQ,CAAC;AACtB,aAAS,IAAI,GAAG,IAAI,KAAK,QAAQ,KAAK;AACpC,YAAM,MAAM,KAAK,CAAC;AAClB,YAAM,SAAS,UAAU,IAAI,MAAM;AAInC,UAAI,UAAU,YAAY,UAAU,WAAY;AAEhD,UAAI,IAAI,WAAW,GAAG;AACpB,YAAI,KAAK,CAAC,MAAM,CAAC;AACjB;AAAA,MACF;AAEA,YAAM,eAAe,gBAAgB,IAAI,aAAa;AACtD,YAAM,aAAa,IAAI,WAAW;AAClC,YAAM,eAAe,IAAI,aAAa;AACtC,UAAI;AAAA,QACF,IAAI,WAAW,IACX,CAAC,QAAQ,cAAc,YAAY,YAAY,IAC/C,CAAC,QAAQ,cAAc,YAAY,cAAc,cAAc,IAAI,WAAW,CAAC;AAAA,MACrF;AAAA,IACF;AAAA,EACF;AACF;AAEA,SAAS,OAAU,KAAU,OAAY;AACvC,WAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,IAAK,KAAI,KAAK,MAAM,CAAC,CAAC;AAC1D;AAEA,SAAS,QAAW,KAAY,OAAoB;AAClD,WAAS,IAAI,IAAI,QAAQ,KAAK,OAAO,IAAK,KAAI,CAAC,IAAI,CAAC;AACpD,SAAO,IAAI,KAAK;AAClB;;;ARhHA,IAAM,gBAAgB;AACtB,IAAM,kBAAkB;AAEjB,IAAM,oBAAoB;AAC1B,IAAM,uBAAuB;AAI7B,IAAM,WAAN,MAAoC;AAAA,EAkBzC,YAAY,KAAyB,QAAwB;AAC3D,UAAM,WAAW,OAAO,QAAQ;AAChC,QAAI,CAAC,YAAa,IAAyC,aAAc,QAAO;AAEhF,UAAM,SAAS,MAAM,GAAwC;AAE7D,UAAM,EAAE,SAAS,MAAM,OAAO,YAAY,SAAS,eAAe,IAAI;AACtE,SAAK,UAAU;AACf,SAAK,OAAO;AACZ,SAAK,QAAQ,SAAS,CAAC;AACvB,SAAK,aAAa;AAClB,SAAK,UAAU;AACf,SAAK,iBAAiB;AACtB,SAAK,aAAa,OAAO,cAAe,OAAkB,uBAAuB;AAEjF,UAAM,UAAU,SAAS,QAAQ,UAAU;AAC3C,SAAK,kBAAkB,QAAQ,IAAI,OAAO;AAE1C,UAAM,EAAE,SAAS,IAAI;AACrB,QAAI,OAAO,aAAa,UAAU;AAChC,WAAK,WAAW;AAChB,WAAK,WAAW;AAAA,IAClB,WAAW,MAAM,QAAQ,QAAQ,GAAG;AAClC,WAAK,WAAW;AAChB,WAAK,WAAW,UAAU,UAAU,QAAQ;AAAA,IAC9C,WAAY,OAAyC,UAAU;AAC7D,YAAM,IAAI,MAAM,4EAA4E;AAAA,IAC9F,OAAO;AACL,YAAM,IAAI,MAAM,uBAAuB,KAAK,UAAU,MAAM,CAAC,EAAE;AAAA,IACjE;AAEA,SAAK,eAAe,cAAc;AAClC,SAAK,aAAa;AAClB,SAAK,iBAAiB;AAAA,EACxB;AACF;AAMA,SAAS,KAAK,KAAyB;AACrC,SAAO;AACT;AAKO,SAAS,gBAAgB,KAA6C;AAzJ7E;AA0JE,UAAQ,gBAAK,GAAG,GAAE,aAAV,eAAU,eAAa,+BAAO,KAAK,GAAG,EAAE,QAAS;AAC3D;AAKO,SAAS,gBAAgB,KAAuD;AAhKvF;AAiKE,UAAQ,UAAK,GAAG,GAAE,aAAV,GAAU,eAAa,+BAAO,KAAK,GAAG,EAAE,QAAS;AAC3D;AAMO,SAAS,aACd,KACA,MACA,QACmC;AACnC,QAAM,UAAU,gBAAgB,GAAG;AAInC,MAAI,QAAQ,QAAQ,OAAQ,QAAO;AAEnC,QAAM,WAAW,QAAQ,IAAI;AAC7B,QAAM,QAAQ;AAAA,IACZ;AAAA,IACA,KAAK,GAAG,EAAE;AAAA,IACV;AAAA,IACA;AAAA,IACA;AAAA,EACF;AAEA,SAAO,UAAU,KAAK,OAAO,SAAS,KAAK;AAC7C;AAOO,SAAS,oBACd,KACA,QAC0C;AAC1C,MAAI,EAAE,MAAM,QAAQ,KAAK,IAAI;AAC7B;AACA,MAAI,OAAO,EAAG,OAAM,IAAI,MAAM,aAAa;AAC3C,MAAI,SAAS,EAAG,OAAM,IAAI,MAAM,eAAe;AAE/C,QAAM,UAAU,gBAAgB,GAAG;AAInC,MAAI,QAAQ,QAAQ,OAAQ,QAAO,SAAS,MAAM,MAAM,MAAM,IAAI;AAElE,QAAM,WAAW,QAAQ,IAAI;AAC7B,QAAM,QAAQ;AAAA,IACZ;AAAA,IACA,KAAK,GAAG,EAAE;AAAA,IACV;AAAA,IACA;AAAA,IACA,QAAQ;AAAA,EACV;AAEA,MAAI,UAAU,GAAI,QAAO,SAAS,MAAM,MAAM,MAAM,IAAI;AAExD,QAAM,UAAU,SAAS,KAAK;AAC9B,MAAI,QAAQ,WAAW,EAAG,QAAO,SAAS,MAAM,MAAM,MAAM,IAAI;AAEhE,QAAM,EAAE,OAAO,gBAAgB,IAAI;AACnC,SAAO;AAAA,IACL,gBAAgB,QAAQ,aAAa,CAAC;AAAA,IACtC,QAAQ,WAAW,IAAI;AAAA,IACvB,QAAQ,aAAa;AAAA,IACrB,QAAQ,WAAW,IAAI,MAAM,QAAQ,WAAW,CAAC,IAAI;AAAA,EACvD;AACF;AAKO,SAAS,qBACd,KACA,QAC4C;AAC5C,QAAM,EAAE,QAAQ,MAAM,QAAQ,KAAK,IAAI;AACvC,SAAO,kBAAkB,KAAK,QAAQ,MAAM,QAAQ,QAAQ,sBAAsB,KAAK;AACzF;AAKO,SAAS,yBAAyB,KAAe,QAA0C;AAChG,QAAM,EAAE,QAAQ,MAAM,QAAQ,KAAK,IAAI;AAEvC,SAAO,kBAAkB,KAAK,QAAQ,MAAM,QAAQ,QAAQ,mBAAmB,IAAI;AACrF;AAKO,SAAS,YAAY,KAAe,IAA0C;AACnF,QAAM,UAAU,gBAAgB,GAAG;AACnC,QAAM,EAAE,OAAO,gBAAgB,IAAI;AAEnC,WAAS,IAAI,GAAG,IAAI,QAAQ,QAAQ,KAAK;AACvC,UAAM,OAAO,QAAQ,CAAC;AACtB,aAAS,IAAI,GAAG,IAAI,KAAK,QAAQ,KAAK;AACpC,YAAM,MAAM,KAAK,CAAC;AAElB,YAAM,gBAAgB,IAAI;AAC1B,YAAM,kBAAkB,IAAI,CAAC;AAC7B,UAAI,SAAS;AACb,UAAI,eAAe;AACnB,UAAI,iBAAiB;AACrB,UAAI,OAAO;AACX,UAAI,IAAI,WAAW,GAAG;AACpB,iBAAS,gBAAgB,IAAI,CAAC,CAAC;AAC/B,uBAAe,IAAI,CAAC,IAAI;AACxB,yBAAiB,IAAI,CAAC;AAAA,MACxB;AACA,UAAI,IAAI,WAAW,EAAG,QAAO,MAAM,IAAI,CAAC,CAAC;AAEzC,SAAG;AAAA,QACD;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF,CAAgB;AAAA,IAClB;AAAA,EACF;AACF;AAEA,SAAS,YAAY,KAAe,QAAwB;AAC1D,QAAM,EAAE,SAAS,gBAAgB,IAAI;AACrC,MAAI,QAAQ,QAAQ,QAAQ,MAAM;AAClC,MAAI,UAAU,GAAI,SAAQ,gBAAgB,QAAQ,MAAM;AACxD,SAAO;AACT;AAKO,SAAS,iBAAiB,KAAe,QAA+B;AAC7E,QAAM,EAAE,eAAe,IAAI;AAC3B,MAAI,kBAAkB,KAAM,QAAO;AACnC,QAAM,QAAQ,YAAY,KAAK,MAAM;AACrC,SAAO,UAAU,KAAK,OAAO,eAAe,KAAK;AACnD;AAKO,SAAS,UAAU,KAAe,QAAyB;AAChE,QAAM,EAAE,WAAW,IAAI;AACvB,MAAI,cAAc,KAAM,QAAO;AAC/B,QAAM,QAAQ,YAAY,KAAK,MAAM;AACrC,SAAO,UAAU,KAAK,QAAQ,WAAW,SAAS,KAAK;AACzD;AAMO,SAAS,oBAAoB,KAAuB,QAA2B;AACpF,QAAM,SAAS,IAAI,SAAS,MAAM,KAAK,CAAC,CAAC,GAAG,MAAM;AAClD,OAAK,MAAM,EAAE,WAAW,IAAI;AAC5B,SAAO;AACT;AAMO,SAAS,WACd,KACkF;AAClF,SAAO,MAAM,KAAK,gBAAgB,GAAG,CAAC;AACxC;AAMO,SAAS,WAAW,KAAiC;AAC1D,SAAO,MAAM,KAAK,gBAAgB,GAAG,CAAC;AACxC;AAEA,SAAS,MACP,KACA,UACwD;AACxD,SAAO;AAAA,IACL,SAAS,IAAI;AAAA,IACb,MAAM,IAAI;AAAA,IACV,OAAO,IAAI;AAAA,IACX,YAAY,IAAI;AAAA,IAChB,SAAS,IAAI;AAAA,IACb,gBAAgB,IAAI;AAAA,IACpB;AAAA,IACA,YAAY,IAAI,cAAe,IAAe;AAAA,EAChD;AACF;AASA,SAAS,SACP,QACA,MACA,QACA,MAC0C;AAC1C,SAAO,EAAE,QAAQ,MAAM,QAAQ,KAAK;AACtC;AAIA,SAAS,SACP,MACA,QAC4C;AAC5C,SAAO,EAAE,MAAM,OAAO;AACxB;AAgBA,SAAS,qBACP,UACA,MACA,MACA,QACA,MACQ;AACR,MAAI,QAAQ,qBAAqB,UAAU,QAAQ,MAAM,IAAI;AAC7D,MAAI,OAAS;AACX,aAAS,SAAS,oBAAoB,aAAa,YAAY,UAAU,QAAQ,KAAK;AAAA,EACxF,WAAW,SAAS,kBAAmB;AAEvC,MAAI,UAAU,MAAM,UAAU,SAAS,OAAQ,QAAO;AACtD,SAAO;AACT;AAEA,SAAS,wBACP,UACA,MACA,MACA,QACA,MACoB;AACpB,MAAI,MAAM,qBAAqB,UAAU,MAAM,MAAM,QAAQ,oBAAoB;AAQjF,MAAI,CAAC,SAAW,SAAS,kBAAmB;AAE5C,MAAI,QAAQ,MAAM,QAAQ,SAAS,OAAQ,QAAO,CAAC;AAKnD,QAAM,gBAAgB,QAAU,SAAS,SAAS,GAAG,EAAE,MAAM;AAG7D,MAAI,CAAC,MAAS,OAAM,WAAW,UAAU,eAAe,GAAG;AAC3D,QAAM,MAAM,WAAW,UAAU,eAAe,GAAG;AAEnD,QAAM,SAAS,CAAC;AAChB,SAAO,OAAO,KAAK,OAAO;AACxB,UAAM,UAAU,SAAS,GAAG;AAC5B,WAAO,KAAK,SAAS,QAAQ,kBAAkB,IAAI,GAAG,QAAQ,oBAAoB,CAAC,CAAC;AAAA,EACtF;AACA,SAAO;AACT;AAkBA,SAAS,kBACP,KACA,QACA,MACA,QACA,MACA,KACiE;AA5dnE;AA6dE;AACA,MAAI,OAAO,EAAG,OAAM,IAAI,MAAM,aAAa;AAC3C,MAAI,SAAS,EAAG,OAAM,IAAI,MAAM,eAAe;AAE/C,QAAM,EAAE,SAAS,gBAAgB,IAAI;AACrC,MAAIC,eAAc,QAAQ,QAAQ,MAAM;AACxC,MAAIA,iBAAgB,GAAI,CAAAA,eAAc,gBAAgB,QAAQ,MAAM;AACpE,MAAIA,iBAAgB,GAAI,QAAO,MAAM,CAAC,IAAI,SAAS,MAAM,IAAI;AAE7D,QAAM,iBAAiB,UAAK,GAAG,GAAE,mBAAV,GAAU,iBAAmB,QAAQ,IAAI,aAAa;AAC7E,QAAM,aAAa,UAAK,GAAG,GAAE,eAAV,GAAU,aAAe,eAAe,gBAAgB,GAAG,GAAG,aAAa;AAE9F,QAAM,WAAW,UAAUA,YAAW,EAAE,IAAI;AAC5C,MAAI,YAAY,KAAM,QAAO,MAAM,CAAC,IAAI,SAAS,MAAM,IAAI;AAE3D,QAAM,OAAO,cAAcA,YAAW;AAEtC,MAAI,IAAK,QAAO,wBAAwB,UAAU,MAAM,MAAM,QAAQ,IAAI;AAE1E,QAAM,QAAQ,qBAAqB,UAAU,MAAM,MAAM,QAAQ,IAAI;AACrE,MAAI,UAAU,GAAI,QAAO,SAAS,MAAM,IAAI;AAE5C,QAAM,UAAU,SAAS,KAAK;AAC9B,SAAO,SAAS,QAAQ,kBAAkB,IAAI,GAAG,QAAQ,oBAAoB,CAAC;AAChF;", "names": ["module", "module", "resolve<PERSON>ri", "sourceIndex", "sourceIndex"]}