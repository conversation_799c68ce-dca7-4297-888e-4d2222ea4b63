"use client"
import React, { useEffect, useState } from 'react'

type Upload = {
  id: number
  filename: string
  status: string
  upload_date: string
  row_count?: number
}

const API_BASE = process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:8000'

export default function Dashboard() {
  const [uploads, setUploads] = useState<Upload[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    const fetchUploads = async () => {
      try {
        const res = await fetch(`${API_BASE}/uploads`)
        if (!res.ok) throw new Error(`Failed: ${res.status}`)
        const data = await res.json()
        setUploads(data)
      } catch (e: any) {
        setError(e.message)
      } finally {
        setLoading(false)
      }
    }
    fetchUploads()
  }, [])

  return (
    <main className="space-y-6">
      <section className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <div className="evos-card">
          <div className="text-sm text-slate">Recent uploads</div>
          <div className="mt-2 text-xl font-semibold">{uploads.length}</div>
        </div>
        <div className="evos-card">
          <div className="text-sm text-slate">System</div>
          <div className="mt-2 text-xl">{API_BASE}</div>
        </div>
        <div className="evos-card">
          <div className="text-sm text-slate">Status</div>
          <div className="mt-2 text-xl">{loading ? 'Loading…' : 'Ready'}</div>
        </div>
      </section>

      <section className="evos-card">
        <h2 className="text-lg font-medium mb-4">Uploads</h2>
        {error && <div className="text-error">{error}</div>}
        <div className="overflow-x-auto">
          <table className="w-full text-sm">
            <thead>
              <tr className="text-left text-slate">
                <th className="py-2">ID</th>
                <th className="py-2">Filename</th>
                <th className="py-2">Status</th>
                <th className="py-2">Rows</th>
                <th className="py-2">Analysis</th>
              </tr>
            </thead>
            <tbody>
              {uploads.map(u => (
                <tr key={u.id} className="border-t border-gray-200">
                  <td className="py-2">{u.id}</td>
                  <td className="py-2">{u.filename}</td>
                  <td className="py-2">{u.status}</td>
                  <td className="py-2">{u.row_count ?? '-'}</td>
                  <td className="py-2">
                    <a className="text-primary-light hover:underline" href={`/uploads/${u.id}`}>View</a>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </section>
    </main>
  )
}

