Operations Intelligence Platform - Backend (No-<PERSON><PERSON> Dev)

Overview
- FastAPI backend with local development defaults:
  - SQLite (`DATABASE_URL=sqlite:///./dev.db`)
  - Local file storage under `storage/uploads`
  - Background processing via FastAPI `BackgroundTasks` (no Celery/Redis)
  - LLM provider set to `stub` for offline/dev

Prerequisites
- Windows PowerShell
- Python 3.11 (or 3.10+)

Quick Start (Windows PowerShell)
1) Create and activate venv
   - `py -3.11 -m venv .venv`
   - `.\.venv\Scripts\Activate.ps1`

2) Install dependencies
   - `pip install -r requirements.txt`

3) Initialize database (SQLite)
   - `python -m app.db.init_db`

4) Start API
   - Option A (simplest): `python start.py` (optional port: `python start.py 8000`)
   - Option B: `uvicorn app.main:app --reload --port 8000`

Environment Variables
- Prefer the repo root `.env` (already added). Backend also reads `backend/.env` if present.
  - Set `OPENROUTER_API_KEY` in root `.env` if you plan to use LLMs.
  - Other defaults are pre-filled for local dev.
- You may also create `backend/.env` if you want backend-only overrides.
  - `DATABASE_URL=sqlite:///./dev.db`
  - `FILE_STORAGE=local`
  - `UPLOAD_DIR=storage/uploads`
  - `ENABLE_CELERY=false`
  - `ENABLE_REDIS=false`
  - `LLM_PROVIDER=openrouter`
  - `MAX_UPLOAD_MB=50`

Endpoints (initial)
- `GET /health` – Service health and config snapshot
- `POST /uploads` – Upload Excel (`.xlsx/.xls`), stored to local disk, enqueued to background processing stub

Notes
- Excel parsing uses pandas/openpyxl. If not installed, upload still stores the file; background processing will no-op and log a warning.
- You can switch to PostgreSQL later by setting `DATABASE_URL` (e.g., `postgresql+psycopg2://user:pass@localhost:5432/opsdb`).
- Celery/Redis can be enabled later; the app is structured to toggle via env flags.
- Python 3.13 note: requires SQLAlchemy >= 2.0.31 (we pin 2.0.35). If you see SQLAlchemy typing assertions, run install again.

Tips
- Opening in browser on Windows PowerShell: `start http://localhost:3000` (don’t type the URL alone as a command)

Addendum: Updated run and env notes
- Top-level helper: from repo root run `./start.ps1` to launch backend and frontend together.
- Endpoints available:
  - `GET /health` – status and config
  - `POST /uploads` – upload Excel
  - `GET /uploads` – list uploads
  - `GET /uploads/{id}` – upload detail
  - `GET /uploads/{id}/analysis` – analysis result
- LLM provider default is `openrouter` but only used if `OPENROUTER_API_KEY` is set in root `.env`.
- First upload test (API):
  1) `POST http://localhost:8000/uploads` with form-data `file`
  2) `GET /uploads` until status is `completed`
  3) `GET /uploads/{id}/analysis`
